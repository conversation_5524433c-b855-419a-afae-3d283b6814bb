#!/usr/bin/env python3
"""
WAV to Opus转换脚本（强制单声道版本）
专为ESP32音频播放项目优化，确保输出为单声道OPUS格式
支持转换为纯Opus流（无OGG容器）或OGG容器格式

特性：
- 强制转换为单声道（自动混音立体声到单声道）
- 支持多种输入格式（WAV, MP3, FLAC, M4A等）
- 针对语音优化的编码参数
- 输出验证确保单声道格式
- 防止立体声包混入

依赖安装：
pip install pydub

系统依赖：
- Ubuntu/Debian: sudo apt install ffmpeg
- macOS: brew install ffmpeg
- Windows: 下载ffmpeg并添加到PATH

使用方法：
python wav_to_opus.py input.wav output.opus
python wav_to_opus.py input.wav output.opus --bitrate 32000
"""

import argparse
import os
import sys
from pathlib import Path

try:
    from pydub import AudioSegment
    from pydub.utils import which
except ImportError:
    print("错误: 请安装pydub库")
    print("运行: pip install pydub")
    sys.exit(1)

def check_ffmpeg():
    """检查ffmpeg是否可用"""
    if not which("ffmpeg"):
        print("错误: 未找到ffmpeg")
        print("请安装ffmpeg:")
        print("  Ubuntu/Debian: sudo apt install ffmpeg")
        print("  macOS: brew install ffmpeg")
        print("  Windows: 下载ffmpeg并添加到PATH")
        return False
    return True

def convert_wav_to_opus(input_file, output_file, bitrate=32000, sample_rate=16000,
                       raw_opus=False, frame_duration=60):
    """
    将音频文件转换为单声道Opus格式（强制单声道）

    Args:
        input_file: 输入音频文件路径
        output_file: 输出Opus文件路径
        bitrate: 比特率 (默认32kbps，适合语音)
        sample_rate: 采样率 (默认16kHz，适合语音)
        raw_opus: 是否输出纯Opus流（无OGG容器）
        frame_duration: 帧长度（毫秒，默认60ms）
    """

    if not os.path.exists(input_file):
        raise FileNotFoundError(f"输入文件不存在: {input_file}")

    print(f"正在转换: {input_file} -> {output_file}")
    print(f"参数: 采样率={sample_rate}Hz, 声道=1(强制单声道), 比特率={bitrate}bps")

    try:
        # 加载音频文件（支持多种格式）
        print("正在加载音频文件...")
        audio = AudioSegment.from_file(input_file)

        # 显示原始文件信息
        original_channels = audio.channels
        original_rate = audio.frame_rate
        duration = len(audio) / 1000.0

        print(f"原始文件信息:")
        print(f"  声道数: {original_channels}")
        print(f"  采样率: {original_rate} Hz")
        print(f"  时长: {duration:.2f} 秒")

        # 强制转换为单声道
        if audio.channels > 1:
            print(f"检测到{audio.channels}声道音频，正在混音到单声道...")
            # 使用pydub的内置混音功能
            audio = audio.set_channels(1)
            print("✅ 混音完成")
        else:
            print("✅ 已是单声道音频")

        # 重采样到目标采样率
        if audio.frame_rate != sample_rate:
            print(f"重采样: {audio.frame_rate}Hz -> {sample_rate}Hz")
            audio = audio.set_frame_rate(sample_rate)
            print("✅ 重采样完成")

        # 构建严格的ffmpeg参数，确保单声道输出
        codec_params = [
            "-c:a", "libopus",
            "-b:a", str(bitrate),
            "-ar", str(sample_rate),
            "-ac", "1",  # 强制单声道
            "-frame_duration", str(frame_duration),
            "-application", "voip",  # 针对语音优化
            "-vbr", "off",  # 关闭可变比特率
            "-compression_level", "10",  # 最高压缩级别
            "-mapping_family", "0",  # 单声道映射
        ]

        if raw_opus:
            # 输出纯Opus流（无OGG容器）
            codec_params.extend(["-f", "opus"])
            print("输出格式: 纯Opus流（无OGG容器）")
        else:
            # 输出OGG容器格式
            codec_params.extend(["-f", "ogg"])
            print("输出格式: OGG容器（强制单声道）")

        print("正在执行OPUS编码...")

        # 执行转换
        audio.export(
            output_file,
            format="opus",
            codec="libopus",
            parameters=codec_params
        )
        
        # 显示文件信息
        input_size = os.path.getsize(input_file)
        output_size = os.path.getsize(output_file)
        compression_ratio = (1 - output_size / input_size) * 100

        print(f"✅ 转换完成!")
        print(f"输入文件大小: {input_size:,} 字节")
        print(f"输出文件大小: {output_size:,} 字节")
        print(f"压缩率: {compression_ratio:.1f}%")

        # 验证输出文件是否为单声道
        if verify_output_mono(output_file):
            print("✅ 验证通过: 输出文件确认为单声道")
        else:
            print("⚠️  警告: 无法验证输出文件声道数")

        return True
        
    except Exception as e:
        print(f"转换失败: {e}")
        return False

def verify_output_mono(file_path):
    """验证输出文件是否为单声道"""
    try:
        # 使用ffprobe验证（更准确）
        import subprocess
        result = subprocess.run([
            'ffprobe', '-v', 'quiet', '-show_entries',
            'stream=channels', '-of', 'csv=p=0', file_path
        ], capture_output=True, text=True)

        if result.returncode == 0:
            channels = int(result.stdout.strip())
            return channels == 1
        else:
            # 回退到pydub验证
            audio = AudioSegment.from_file(file_path)
            return audio.channels == 1

    except Exception as e:
        print(f"验证时出错: {e}")
        return False

def analyze_audio_file(file_path):
    """分析音频文件信息"""
    try:
        audio = AudioSegment.from_file(file_path)

        print(f"\n📊 音频文件信息: {file_path}")
        print(f"  时长: {len(audio) / 1000:.2f} 秒")
        print(f"  采样率: {audio.frame_rate} Hz")
        print(f"  声道数: {audio.channels}")
        print(f"  位深: {audio.sample_width * 8} bit")
        print(f"  文件大小: {os.path.getsize(file_path):,} 字节")

        # 给出转换建议
        if audio.channels > 1:
            print(f"  💡 建议: 检测到{audio.channels}声道音频，转换时将自动混音到单声道")
        else:
            print(f"  ✅ 已是单声道音频")

        if audio.frame_rate != 16000:
            print(f"  💡 建议: 当前采样率{audio.frame_rate}Hz，将重采样到16000Hz")
        else:
            print(f"  ✅ 采样率已是16000Hz")

    except Exception as e:
        print(f"❌ 无法分析文件: {e}")

def main():
    parser = argparse.ArgumentParser(
        description="音频到单声道Opus转换工具（ESP32专用）",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 转换为单声道OGG容器格式（推荐）
  python wav_to_opus.py music.wav music.opus

  # 转换为纯Opus流（无OGG容器）
  python wav_to_opus.py music.wav music.opus --raw

  # 自定义参数（强制单声道）
  python wav_to_opus.py music.wav music.opus --bitrate 32000 --sample-rate 16000

  # 分析音频文件
  python wav_to_opus.py --analyze music.wav

注意:
  - 所有输出都会强制转换为单声道
  - 立体声输入会自动混音到单声道
  - 专为ESP32 OPUS解码器优化
        """
    )
    
    parser.add_argument("input", nargs="?", help="输入WAV文件路径")
    parser.add_argument("output", nargs="?", help="输出Opus文件路径")
    
    parser.add_argument("--raw", action="store_true",
                       help="输出纯Opus流（无OGG容器）")
    parser.add_argument("--bitrate", type=int, default=32000,
                       help="比特率 (默认: 32000，适合语音)")
    parser.add_argument("--sample-rate", type=int, default=16000,
                       help="采样率 (默认: 16000Hz)")
    parser.add_argument("--frame-duration", type=int, default=60,
                       help="帧长度毫秒 (默认: 60ms)")
    parser.add_argument("--analyze", action="store_true",
                       help="仅分析输入文件信息，不转换")
    parser.add_argument("--verify", action="store_true", default=True,
                       help="验证输出文件格式（默认启用）")
    
    args = parser.parse_args()
    
    # 检查ffmpeg
    if not check_ffmpeg():
        sys.exit(1)
    
    # 分析模式
    if args.analyze:
        if not args.input:
            print("错误: 请指定要分析的文件")
            sys.exit(1)
        analyze_audio_file(args.input)
        return
    
    # 转换模式
    if not args.input or not args.output:
        parser.print_help()
        sys.exit(1)
    
    # 验证输入文件
    if not os.path.exists(args.input):
        print(f"错误: 输入文件不存在: {args.input}")
        sys.exit(1)
    
    # 创建输出目录
    output_dir = os.path.dirname(args.output)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 分析输入文件
    analyze_audio_file(args.input)
    
    # 执行转换（移除channels参数，强制单声道）
    success = convert_wav_to_opus(
        args.input,
        args.output,
        bitrate=args.bitrate,
        sample_rate=args.sample_rate,
        raw_opus=args.raw,
        frame_duration=args.frame_duration
    )
    
    if success:
        print(f"\n🎉 转换成功: {args.output}")
        if args.raw:
            print("💡 提示: 生成的是纯Opus流，适合ESP32直接播放")
        else:
            print("💡 提示: 生成的是单声道OGG容器格式，兼容ESP32解码器")
        print("🔧 专为ESP32优化: 单声道、16kHz、语音编码")
    else:
        print("❌ 转换失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
