# SK-Terminal 项目说明文档

## 项目概述

SK-Terminal 是一个基于 ESP32-S3 的智能终端项目，由深圳市泛途智能科技有限公司开发。该项目实现了一个功能完整的音频通信终端，支持 WiFi 连接、OPUS 音频编解码、HTTP 文件下载、OTA 升级等功能。

### 主要特性

- **硬件平台**：ESP32-S3 (支持 PSRAM)
- **开发框架**：ESP-IDF
- **音频编解码**：OPUS 格式
- **网络功能**：WiFi、HTTP 客户端、WebSocket
- **存储管理**：PSRAM、NVS 配置存储
- **升级机制**：OTA 远程升级
- **音频处理**：实时语音识别、音频播放

## sk_http_downloader 模块详细分析

### 模块概述

`sk_http_downloader.c` 是项目中的网络下载模块，主要负责：
1. HTTP 文件下载到 PSRAM 环形缓冲区
2. OGG 容器格式的 OPUS 音频解析
3. OPUS 音频包解码适配

<augment_code_snippet path="main/network/sk_http_downloader.c" mode="EXCERPT">
````c
/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_http_downloader.c
 * @description: HTTP文件下载到PSRAM模块实现
 * @author: Carl Luo
 * @date: 2025-01-20
 */
````
</augment_code_snippet>

### 核心数据结构

<augment_code_snippet path="main/include/sk_http_downloader.h" mode="EXCERPT">
````c
typedef struct {
    RingbufHandle_t ringbuf;
    uint8_t *storage;
    size_t totalSize;
} SkHttpDownloadData;

typedef struct {
    uint8_t version;        // OPUS版本
    uint8_t channels;       // 声道数
    uint16_t preSkip;       // 预跳过样本数
    uint32_t sampleRate;    // 采样率
    uint16_t outputGain;    // 输出增益
} SkOpusHeader;
````
</augment_code_snippet>

### 核心功能实现

#### 1. HTTP 文件下载

**函数**：`SkHttpDownloadFile(const char *url, SkHttpDownloadData *downloadData)`

**实现特点**：
- 使用 ESP-IDF HTTP 客户端进行下载
- 在 PSRAM 中分配 2MB 环形缓冲区存储
- 支持下载进度显示（每 10% 输出一次）
- 完整的错误处理和资源清理机制

<augment_code_snippet path="main/network/sk_http_downloader.c" mode="EXCERPT">
````c
#define HTTP_BUFFER_SIZE        4096
#define HTTP_TIMEOUT_MS         30000
#define RINGBUF_SIZE            (2 * 1024 * 1024)  

sk_err_t SkHttpDownloadFile(const char *url, SkHttpDownloadData *downloadData) {
    if (url == NULL || downloadData == NULL) {
        SK_LOGE(TAG, "Invalid parameters");
        return SK_RET_INVALID_PARAM;
    }
````
</augment_code_snippet>

#### 2. OGG 格式解析

**核心函数**：
- `InitOggParser()`: 初始化解析器，从环形缓冲区获取数据
- `SkOggParseOpusHeader()`: 解析 OPUS 头部信息
- `SkOggGetNextOpusPacket()`: 提取下一个 OPUS 音频包

**解析流程**：
1. 验证 OGG 魔数 "OggS"
2. 跳过 OPUS 头部页面和注释页面
3. 逐页解析音频数据包
4. 提取纯 OPUS 数据供解码使用

#### 3. OPUS 解码适配

**函数**：`SkOpusAdapterDecode(SkOpusPacket *packet, int16_t *pcmBuffer, int32_t pcmSize)`

**特点**：
- 复用现有的 `SkOpusDecHandler` 解码器
- 支持 16kHz 采样率，单声道
- 每帧 960 样本（60ms 音频）
- 集成到项目的音频处理流程

### 内存管理策略

```c
// 在PSRAM中分配环形缓冲区存储空间（包含数据空间和结构体空间）
size_t totalSize = RINGBUF_SIZE + sizeof(StaticRingbuffer_t);
downloadData->storage = heap_caps_malloc(totalSize, MALLOC_CAP_SPIRAM);

// 结构体放在存储空间的末尾
StaticRingbuffer_t *structure = (StaticRingbuffer_t*)(downloadData->storage + RINGBUF_SIZE);

// 创建静态环形缓冲区
downloadData->ringbuf = xRingbufferCreateStatic(RINGBUF_SIZE, RINGBUF_TYPE_BYTEBUF,
                                                downloadData->storage, structure);
```

### 错误处理机制

模块实现了完整的错误处理：

1. **参数验证**：检查 URL 和输出参数有效性
2. **网络错误**：HTTP 连接失败、读取错误处理
3. **内存错误**：PSRAM 分配失败、环形缓冲区创建失败
4. **文件大小检查**：限制最大文件大小为 2MB
5. **资源清理**：任何错误情况下都会正确清理已分配的资源

## 项目整体架构

### 模块组织结构

```
sk-terminal/
├── main/
│   ├── app/           # 应用层状态机
│   ├── audio/         # 音频处理模块
│   ├── board/         # 硬件抽象层
│   ├── config/        # 配置管理
│   ├── network/       # 网络通信（包括HTTP下载器）
│   ├── opus_coder/    # OPUS编解码
│   ├── protocol/      # 通信协议
│   ├── ota/          # OTA升级
│   ├── os/           # 操作系统适配
│   └── include/      # 公共头文件
├── managed_components/ # ESP组件依赖
└── tools/            # 开发工具
```

### 核心模块依赖关系

```mermaid
graph TD
    A[sk_http_downloader] --> B[esp_http_client]
    A --> C[freertos/ringbuf]
    A --> D[esp_heap_caps]
    A --> E[sk_opus_dec]
    A --> F[sk_log]
    
    E --> G[opus库]
    F --> H[esp_log]
    
    I[应用层] --> A
    I --> J[sk_audio]
    I --> K[sk_sm状态机]
```

## 编程规范和最佳实践

### 命名规范

- **函数命名**：camelCase，首字母大写（如 `SkHttpDownloadFile`）
- **常量命名**：camelCase（如 `HTTP_BUFFER_SIZE`）
- **类型定义**：Pascal命名 + 后缀（如 `SkHttpDownloadData`）
- **错误码**：统一使用 `sk_err_t` 类型

### 错误码定义

<augment_code_snippet path="main/include/sk_common.h" mode="EXCERPT">
````c
typedef enum {
    SK_RET_SUCCESS = 0,
    SK_RET_FAIL = 1,
    SK_RET_INVALID_PARAM = 2,
    SK_RET_NOT_IMPLEMENTED = 3,
    SK_RET_NO_MEMORY = 4,
    SK_RET_INVALID_OPERATION = 5,
    SK_RET_TIMEOUT = 6,
    SK_RET_INTERRUPTED = 7,
    SK_RET_NOT_FOUND = 8,
    SK_RET_ALREADY_EXISTS = 9,
    SK_RET_NOT_READY = 10,
} SkRetDef_e;
````
</augment_code_snippet>

### 日志系统

<augment_code_snippet path="main/include/sk_log.h" mode="EXCERPT">
````c
#define SK_LOGE(tag, format, ...) ESP_LOGE(tag, format, ##__VA_ARGS__)
#define SK_LOGW(tag, format, ...) ESP_LOGW(tag, format, ##__VA_ARGS__)
#define SK_LOGI(tag, format, ...) ESP_LOGI(tag, format, ##__VA_ARGS__)
#define SK_LOGD(tag, format, ...) ESP_LOGD(tag, format, ##__VA_ARGS__)
````
</augment_code_snippet>

## 依赖关系和配置

### 主要依赖组件

- **ESP-IDF 组件**：esp_http_client, freertos, esp_heap_caps
- **第三方组件**：esp-opus (OPUS 编解码库)
- **项目内部模块**：sk_opus_dec, sk_log, sk_common

### 关键配置项

1. **PSRAM 支持**：必须启用 ESP32-S3 的 PSRAM 功能
2. **WiFi 配置**：需要正确配置 WiFi 连接
3. **内存配置**：确保有足够的 PSRAM 空间（至少 2MB）
4. **HTTP 超时**：默认 30 秒，可根据网络环境调整

## 使用示例

### 基本使用流程

```c
// 1. 下载文件
SkHttpDownloadData downloadData;
sk_err_t ret = SkHttpDownloadFile("http://example.com/audio.ogg", &downloadData);
if (ret != SK_RET_SUCCESS) {
    SK_LOGE("APP", "Download failed: %d", ret);
    return;
}

// 2. 解析OPUS头部
SkOpusHeader header;
ret = SkOggParseOpusHeader(downloadData.ringbuf, &header);
if (ret == SK_RET_SUCCESS) {
    SK_LOGI("APP", "OPUS: %dch, %luHz", header.channels, header.sampleRate);
}

// 3. 解码音频包
SkOpusPacket packet;
int16_t pcmBuffer[960];
while (SkOggGetNextOpusPacket(downloadData.ringbuf, &packet) == SK_RET_SUCCESS) {
    int32_t samples = SkOpusAdapterDecode(&packet, pcmBuffer, 960);
    if (samples > 0) {
        // 处理PCM数据
    }
    SkOggFreeOpusPacket(&packet);
}

// 4. 清理资源
SkHttpDownloadFree(&downloadData);
SkOggResetParser();
```

## 测试和调试

### 测试功能

模块提供了 `SkOpusAdapterTest()` 函数用于测试 OPUS 解码功能：
- 解码前 10 个音频包
- 显示解码统计信息
- 验证 PCM 输出数据

### 调试信息

- 下载进度实时显示
- 详细的错误日志输出
- OPUS 解码统计信息
- 内存使用情况监控

## 注意事项和限制

### 系统要求

1. **硬件要求**：ESP32-S3 + PSRAM（至少 2MB）
2. **网络要求**：稳定的 WiFi 连接
3. **内存要求**：足够的 PSRAM 空间用于文件缓存

### 使用限制

1. **文件大小限制**：最大支持 2MB 文件下载
2. **格式支持**：仅支持 OGG 容器的 OPUS 音频
3. **并发限制**：同时只能进行一个下载任务
4. **网络超时**：默认 30 秒超时，大文件需要稳定网络

### 最佳实践

1. **资源管理**：必须调用 `SkHttpDownloadFree()` 释放资源
2. **错误处理**：检查所有函数返回值
3. **内存监控**：监控 PSRAM 使用情况
4. **网络稳定性**：确保下载过程中网络连接稳定

## 总结

sk_http_downloader 模块是 SK-Terminal 项目中的重要组件，实现了高效的 HTTP 文件下载和 OPUS 音频解析功能。该模块设计合理，错误处理完善，为项目的音频处理功能提供了可靠的数据源。通过合理使用 PSRAM 和环形缓冲区，实现了内存高效的大文件处理能力。
