/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_http_downloader.c
 * @description: HTTP文件下载到PSRAM模块实现
 * @author: <PERSON>
 * @date: 2025-01-20
 */
#include <string.h>
#include <stdlib.h>
#include "esp_log.h"
#include "esp_http_client.h"
#include "esp_heap_caps.h"
#include "freertos/FreeRTOS.h"
#include "freertos/ringbuf.h"
#include "sk_http_downloader.h"
#include "sk_opus_dec.h"
#include "sk_log.h"

static const char *TAG = "SkHttpDownloader";

#define HTTP_BUFFER_SIZE        4096
#define HTTP_TIMEOUT_MS         30000
#define RINGBUF_SIZE            (2 * 1024 * 1024)  

sk_err_t SkHttpDownloadFile(const char *url, SkHttpDownloadData *downloadData) {
    if (url == NULL || downloadData == NULL) {
        SK_LOGE(TAG, "Invalid parameters");
        return SK_RET_INVALID_PARAM;
    }

    memset(downloadData, 0, sizeof(SkHttpDownloadData));
    
    SK_LOGI(TAG, "Starting download: %s", url);
    
    // 配置HTTP客户端
    esp_http_client_config_t config = {
        .url = url,
        .timeout_ms = HTTP_TIMEOUT_MS,
    };
    
    esp_http_client_handle_t client = esp_http_client_init(&config);
    if (client == NULL) {
        SK_LOGE(TAG, "Failed to init HTTP client");
        return SK_RET_FAIL;
    }
    
    // 开始请求
    esp_err_t err = esp_http_client_open(client, 0);
    if (err != ESP_OK) {
        SK_LOGE(TAG, "Failed to open HTTP connection: %s", esp_err_to_name(err));
        esp_http_client_cleanup(client);
        return SK_RET_FAIL;
    }
    
    // 获取文件大小
    int content_length = esp_http_client_fetch_headers(client);
    if (content_length <= 0 || content_length > RINGBUF_SIZE) {
        SK_LOGE(TAG, "Invalid file size: %d", content_length);
        esp_http_client_close(client);
        esp_http_client_cleanup(client);
        return SK_RET_FAIL;
    }
    
    SK_LOGI(TAG, "File size: %d bytes", content_length);
    
    // 在PSRAM中分配环形缓冲区存储空间（包含数据空间和结构体空间）
    size_t totalSize = RINGBUF_SIZE + sizeof(StaticRingbuffer_t);
    downloadData->storage = heap_caps_malloc(totalSize, MALLOC_CAP_SPIRAM);
    if (downloadData->storage == NULL) {
        SK_LOGE(TAG, "Failed to allocate PSRAM: %zu bytes", totalSize);
        esp_http_client_close(client);
        esp_http_client_cleanup(client);
        return SK_RET_NO_MEMORY;
    }

    // 结构体放在存储空间的末尾
    StaticRingbuffer_t *structure = (StaticRingbuffer_t*)(downloadData->storage + RINGBUF_SIZE);

    // 创建静态环形缓冲区
    downloadData->ringbuf = xRingbufferCreateStatic(RINGBUF_SIZE, RINGBUF_TYPE_BYTEBUF,
                                                    downloadData->storage, structure);
    if (downloadData->ringbuf == NULL) {
        SK_LOGE(TAG, "Failed to create static ringbuffer");
        heap_caps_free(downloadData->storage);
        esp_http_client_close(client);
        esp_http_client_cleanup(client);
        return SK_RET_FAIL;
    }
    
    SK_LOGI(TAG, "Created ringbuffer in PSRAM: %d bytes", RINGBUF_SIZE);
    
    // 分配HTTP读取缓冲区
    uint8_t *buffer = malloc(HTTP_BUFFER_SIZE);
    if (buffer == NULL) {
        SK_LOGE(TAG, "Failed to allocate HTTP buffer");
        vRingbufferDelete(downloadData->ringbuf);
        heap_caps_free(downloadData->storage);
        esp_http_client_close(client);
        esp_http_client_cleanup(client);
        return SK_RET_NO_MEMORY;
    }
    
    // 下载数据到环形缓冲区
    size_t total_read = 0;
    while (total_read < content_length) {
        int data_read = esp_http_client_read(client, (char*)buffer, HTTP_BUFFER_SIZE);
        if (data_read < 0) {
            SK_LOGE(TAG, "HTTP read error: %d", data_read);
            free(buffer);
            vRingbufferDelete(downloadData->ringbuf);
            heap_caps_free(downloadData->storage);
            esp_http_client_close(client);
            esp_http_client_cleanup(client);
            return SK_RET_FAIL;
        }
        
        if (data_read == 0) {
            break;
        }
        
        if (total_read + data_read > content_length) {
            data_read = content_length - total_read;
        }
        
        // 写入环形缓冲区
        if (xRingbufferSend(downloadData->ringbuf, buffer, data_read, portMAX_DELAY) != pdTRUE) {
            SK_LOGE(TAG, "Failed to write to ringbuffer");
            free(buffer);
            vRingbufferDelete(downloadData->ringbuf);
            heap_caps_free(downloadData->storage);
            esp_http_client_close(client);
            esp_http_client_cleanup(client);
            return SK_RET_FAIL;
        }
        
        total_read += data_read;
        
        // 显示进度
        if (total_read % (content_length / 10) == 0 || total_read == content_length) {
            SK_LOGI(TAG, "Download progress: %zu/%d bytes (%d%%)", 
                   total_read, content_length, (int)(total_read * 100 / content_length));
        }
    }
    
    free(buffer);
    esp_http_client_close(client);
    esp_http_client_cleanup(client);
    
    if (total_read != content_length) {
        SK_LOGE(TAG, "Download incomplete: %zu/%d bytes", total_read, content_length);
        vRingbufferDelete(downloadData->ringbuf);
        heap_caps_free(downloadData->storage);
        return SK_RET_FAIL;
    }

    SK_LOGI(TAG, "Download completed: %zu bytes", total_read);
    downloadData->totalSize = total_read;
    
    return SK_RET_SUCCESS;
}

void SkHttpDownloadFree(SkHttpDownloadData *downloadData) {
    if (downloadData == NULL) {
        return;
    }

    if (downloadData->ringbuf != NULL) {
        vRingbufferDelete(downloadData->ringbuf);
        downloadData->ringbuf = NULL;
    }

    if (downloadData->storage != NULL) {
        heap_caps_free(downloadData->storage);
        downloadData->storage = NULL;
    }

    downloadData->totalSize = 0;
    SK_LOGI(TAG, "Download data freed from PSRAM");
}

// ==================== OGG解析功能 ====================

static uint8_t *g_audioData = NULL;
static size_t g_audioSize = 0;
static size_t g_currentPos = 0;
static bool g_initialized = false;

/**
 * @brief 初始化OGG解析器，从环形缓冲区获取所有数据
 */
static sk_err_t InitOggParser(RingbufHandle_t ringbuf) {
    if (g_initialized) {
        return SK_RET_SUCCESS;
    }

    // 从环形缓冲区获取所有数据
    size_t itemSize;
    uint8_t *item = xRingbufferReceive(ringbuf, &itemSize, pdMS_TO_TICKS(1000));
    if (item == NULL) {
        return SK_RET_TIMEOUT;
    }

    // 分配内存并复制数据
    g_audioData = malloc(itemSize);
    if (g_audioData == NULL) {
        vRingbufferReturnItem(ringbuf, item);
        return SK_RET_NO_MEMORY;
    }

    memcpy(g_audioData, item, itemSize);
    g_audioSize = itemSize;
    g_currentPos = 0;
    g_initialized = true;

    vRingbufferReturnItem(ringbuf, item);

    SK_LOGI(TAG, "OGG parser initialized with %zu bytes", g_audioSize);
    return SK_RET_SUCCESS;
}

/**
 * @brief 解析OPUS头部信息
 */
sk_err_t SkOggParseOpusHeader(RingbufHandle_t ringbuf, SkOpusHeader *header) {
    if (ringbuf == NULL || header == NULL) {
        return SK_RET_INVALID_PARAM;
    }

    // 初始化解析器
    sk_err_t ret = InitOggParser(ringbuf);
    if (ret != SK_RET_SUCCESS) {
        return ret;
    }

    // 验证OGG格式
    if (g_audioSize < 46 || memcmp(g_audioData, "OggS", 4) != 0) {
        return SK_RET_FAIL;
    }

    // 找到OPUS头部数据（跳过OGG页面头部和段表）
    uint8_t segments = g_audioData[26];
    uint8_t *pageData = g_audioData + 27 + segments;

    // 验证OPUS头部
    if (memcmp(pageData, "OpusHead", 8) != 0) {
        return SK_RET_FAIL;
    }

    // 解析OPUS参数
    header->version = pageData[8];
    header->channels = pageData[9];
    header->preSkip = pageData[10] | (pageData[11] << 8);
    header->sampleRate = pageData[12] | (pageData[13] << 8) | (pageData[14] << 16) | (pageData[15] << 24);
    header->outputGain = pageData[16] | (pageData[17] << 8);

    SK_LOGI(TAG, "OPUS: %dch, %luHz, preSkip:%d",
           header->channels, header->sampleRate, header->preSkip);

    return SK_RET_SUCCESS;
}

/**
 * @brief 从缓存数据获取下一个OPUS数据包
 */
sk_err_t SkOggGetNextOpusPacket(RingbufHandle_t ringbuf, SkOpusPacket *packet) {
    if (ringbuf == NULL || packet == NULL) {
        return SK_RET_INVALID_PARAM;
    }

    // 确保解析器已初始化
    if (!g_initialized) {
        sk_err_t ret = InitOggParser(ringbuf);
        if (ret != SK_RET_SUCCESS) {
            return ret;
        }
    }

    // 第一次调用时，跳过头部和注释页面
    static bool firstCall = true;
    if (firstCall) {
        g_currentPos = 0;

        // 跳过第一个页面（OPUS头部）
        if (memcmp(g_audioData, "OggS", 4) == 0) {
            uint8_t segments1 = g_audioData[26];
            size_t page1Size = 27 + segments1;
            for (int i = 0; i < segments1; i++) {
                page1Size += g_audioData[27 + i];
            }
            g_currentPos = page1Size;
        }

        // 跳过第二个页面（注释）
        if (g_currentPos + 27 < g_audioSize && memcmp(g_audioData + g_currentPos, "OggS", 4) == 0) {
            uint8_t segments2 = g_audioData[g_currentPos + 26];
            size_t page2Size = 27 + segments2;
            for (int i = 0; i < segments2; i++) {
                page2Size += g_audioData[g_currentPos + 27 + i];
            }
            g_currentPos += page2Size;
        }

        firstCall = false;
        SK_LOGI(TAG, "Audio data starts at: %zu", g_currentPos);
    }

    // 检查是否还有数据
    if (g_currentPos + 27 >= g_audioSize) {
        return SK_RET_NOT_FOUND;
    }

    // 验证当前页面
    if (memcmp(g_audioData + g_currentPos, "OggS", 4) != 0) {
        return SK_RET_FAIL;
    }

    // 提取OPUS数据
    uint8_t segments = g_audioData[g_currentPos + 26];
    size_t headerSize = 27 + segments;
    size_t pageDataSize = 0;

    for (int i = 0; i < segments; i++) {
        pageDataSize += g_audioData[g_currentPos + 27 + i];
    }

    // 检查数据完整性
    if (g_currentPos + headerSize + pageDataSize > g_audioSize) {
        return SK_RET_FAIL;
    }

    // 分配并复制数据
    packet->data = malloc(pageDataSize);
    if (packet->data == NULL) {
        return SK_RET_NO_MEMORY;
    }

    memcpy(packet->data, g_audioData + g_currentPos + headerSize, pageDataSize);
    packet->size = pageDataSize;

    // 更新位置
    g_currentPos += headerSize + pageDataSize;

    SK_LOGI(TAG, "OPUS packet: %zu bytes", packet->size);
    return SK_RET_SUCCESS;
}

/**
 * @brief 重置OGG解析状态（用于重新开始解析）
 */
void SkOggResetParser(void) {
    if (g_audioData) {
        free(g_audioData);
        g_audioData = NULL;
    }
    g_audioSize = 0;
    g_currentPos = 0;
    g_initialized = false;
    SK_LOGI(TAG, "OGG parser reset");
}

/**
 * @brief 释放OPUS包资源
 */
void SkOggFreeOpusPacket(SkOpusPacket *packet) {
    if (packet && packet->data) {
        free(packet->data);
        packet->data = NULL;
        packet->size = 0;
    }
}

// ==================== OPUS解码适配功能 ====================

/**
 * @brief 验证OPUS包的基本有效性
 */
static bool ValidateOpusPacket(SkOpusPacket *packet) {
    if (packet == NULL || packet->data == NULL || packet->size == 0) {
        SK_LOGE(TAG, "Invalid packet parameters");
        return false;
    }

    if (packet->size > 1275) {  // OPUS规范最大包大小
        SK_LOGW(TAG, "Packet size %zu exceeds OPUS maximum (1275 bytes)", packet->size);
        // 不返回false，因为可能是多帧包
    }

    uint8_t toc = packet->data[0];
    int config = (toc >> 3) & 0x1F;
    int frameCountCode = toc & 0x3;

    // 验证配置值
    if (config > 31) {
        SK_LOGE(TAG, "Invalid OPUS config: %d", config);
        return false;
    }

    // 验证帧数代码对应的包结构
    switch (frameCountCode) {
        case 0: // 1帧
            if (packet->size < 1) {
                SK_LOGE(TAG, "Single frame packet too small: %zu", packet->size);
                return false;
            }
            break;

        case 1: // 2帧等长
            if (packet->size < 2) {
                SK_LOGE(TAG, "Two equal frames packet too small: %zu", packet->size);
                return false;
            }
            break;

        case 2: // 2帧不等长
            if (packet->size < 3) {
                SK_LOGE(TAG, "Two unequal frames packet too small: %zu", packet->size);
                return false;
            }
            break;

        case 3: // 可变帧数
            if (packet->size < 2) {
                SK_LOGE(TAG, "Variable frames packet too small: %zu", packet->size);
                return false;
            }
            break;
    }

    return true;
}

/**
 * @brief 分析OPUS包结构的调试函数
 */
static void AnalyzeOpusPacket(SkOpusPacket *packet) {
    if (packet == NULL || packet->data == NULL || packet->size == 0) {
        SK_LOGE(TAG, "Invalid packet for analysis");
        return;
    }

    SK_LOGI(TAG, "=== OPUS Packet Analysis ===");
    SK_LOGI(TAG, "  Total size: %zu bytes", packet->size);

    // 分析TOC字节
    uint8_t toc = packet->data[0];
    int config = (toc >> 3) & 0x1F;
    int stereo = (config >= 12) ? 1 : 0;
    int frameCountCode = toc & 0x3;

    const char* frameTypes[] = {"1 frame", "2 equal frames", "2 unequal frames", "variable frames"};
    const char* modes[] = {"SILK", "Hybrid", "CELT"};
    int mode = (config < 12) ? 0 : ((config < 16) ? 1 : 2);

    SK_LOGI(TAG, "  TOC byte: 0x%02x", toc);
    SK_LOGI(TAG, "  Config: %d (Mode: %s, %s)", config, modes[mode], stereo ? "Stereo" : "Mono");
    SK_LOGI(TAG, "  Frame type: %s (code: %d)", frameTypes[frameCountCode], frameCountCode);

    // 显示前32字节的十六进制数据
    int displayBytes = (packet->size > 32) ? 32 : packet->size;
    SK_LOGI(TAG, "  First %d bytes (hex):", displayBytes);

    for (int i = 0; i < displayBytes; i += 16) {
        char hexStr[64] = {0};
        char asciiStr[17] = {0};
        int lineBytes = (displayBytes - i > 16) ? 16 : (displayBytes - i);

        for (int j = 0; j < lineBytes; j++) {
            sprintf(hexStr + j*3, "%02x ", packet->data[i + j]);
            asciiStr[j] = (packet->data[i + j] >= 32 && packet->data[i + j] <= 126) ?
                         packet->data[i + j] : '.';
        }
        SK_LOGI(TAG, "    %04x: %-48s |%s|", i, hexStr, asciiStr);
    }

    // 根据帧类型分析结构
    switch (frameCountCode) {
        case 0: // 1帧
            SK_LOGI(TAG, "  Single frame: %zu bytes payload", packet->size - 1);
            break;

        case 1: // 2帧等长
            SK_LOGI(TAG, "  Two equal frames: %zu bytes each", (packet->size - 1) / 2);
            break;

        case 2: // 2帧不等长
            if (packet->size >= 2) {
                uint8_t frame1Size = packet->data[1];
                size_t frame2Size = packet->size - 2 - frame1Size;
                SK_LOGI(TAG, "  Two unequal frames: %d + %zu bytes", frame1Size, frame2Size);
            }
            break;

        case 3: // 可变帧数
            if (packet->size >= 2) {
                uint8_t frameCount = packet->data[1] & 0x3F;
                bool vbr = (packet->data[1] & 0x80) != 0;
                bool padding = (packet->data[1] & 0x40) != 0;
                SK_LOGI(TAG, "  Variable frames: %d frames, VBR: %s, Padding: %s",
                       frameCount, vbr ? "Yes" : "No", padding ? "Yes" : "No");
            }
            break;
    }

    SK_LOGI(TAG, "=== End Analysis ===");
}

/**
 * @brief 使用现有解码器解码OPUS包
 */
int32_t SkOpusAdapterDecode(SkOpusPacket *packet, int16_t *pcmBuffer, int32_t pcmSize) {
    if (packet == NULL || packet->data == NULL || pcmBuffer == NULL) {
        return 0;
    }

    // 验证包的基本有效性
    if (!ValidateOpusPacket(packet)) {
        SK_LOGE(TAG, "OPUS packet validation failed");
        return 0;
    }

    // 添加包结构分析
    static int packetAnalysisCount = 0;
    if (packetAnalysisCount < 3) {  // 只分析前3个包，避免日志过多
        AnalyzeOpusPacket(packet);
        packetAnalysisCount++;
    }

    // 获取现有的OPUS解码器句柄
    SkOpusDecHandler handler = SkOpusDecGetHandler();
    if (handler == NULL) {
        SK_LOGE(TAG, "OPUS decoder not initialized");
        return 0;
    }

    // 调用现有的解码函数
    int32_t samples = SkOpusDecDecode(handler, packet->data, packet->size, pcmBuffer, pcmSize);
    if (samples > 0) {
        SK_LOGI(TAG, "Decoded OPUS: %zu bytes -> %ld samples", packet->size, samples);
    } else {
        SK_LOGE(TAG, "OPUS decode failed: %ld (error code: %ld)", samples, samples);

        // 对于失败的包，显示更多调试信息
        if (packetAnalysisCount >= 3) {
            SK_LOGI(TAG, "Failed packet TOC: 0x%02x, size: %zu", packet->data[0], packet->size);
        }
    }

    return samples;
}

/**
 * @brief 测试OPUS解码功能
 */
sk_err_t SkOpusAdapterTest(SkHttpDownloadData *downloadData) {
    if (downloadData == NULL) {
        return SK_RET_INVALID_PARAM;
    }

    SK_LOGI(TAG, "=== Testing OPUS Decoding ===");

    // 显示解码器状态信息
    SkOpusDecHandler handler = SkOpusDecGetHandler();
    SK_LOGI(TAG, "OPUS decoder handler: %p", handler);
    if (handler != NULL) {
        SK_LOGI(TAG, "Resetting OPUS decoder state...");
        SkOpusDecStop(); // 重置解码器状态
    }

    // 确保解析器已初始化
    if (!g_initialized) {
        sk_err_t ret = InitOggParser(downloadData->ringbuf);
        if (ret != SK_RET_SUCCESS) {
            return ret;
        }
    }

    SK_LOGI(TAG, "Audio data size: %zu bytes", g_audioSize);

    // 分配PCM缓冲区（16kHz, 1ch, 60ms = 960样本）
    const int32_t PCM_FRAME_SIZE = 960;
    int16_t *pcmBuffer = malloc(PCM_FRAME_SIZE * sizeof(int16_t));
    if (pcmBuffer == NULL) {
        SK_LOGE(TAG, "Failed to allocate PCM buffer");
        return SK_RET_NO_MEMORY;
    }

    int packetCount = 0;
    int successCount = 0;
    size_t totalSamples = 0;

    // 重置解析状态
    static bool firstCall = true;
    if (firstCall) {
        g_currentPos = 0;

        // 跳过头部和注释页面
        if (memcmp(g_audioData, "OggS", 4) == 0) {
            uint8_t segments1 = g_audioData[26];
            size_t page1Size = 27 + segments1;
            for (int i = 0; i < segments1; i++) {
                page1Size += g_audioData[27 + i];
            }
            g_currentPos = page1Size;
        }

        if (g_currentPos + 27 < g_audioSize && memcmp(g_audioData + g_currentPos, "OggS", 4) == 0) {
            uint8_t segments2 = g_audioData[g_currentPos + 26];
            size_t page2Size = 27 + segments2;
            for (int i = 0; i < segments2; i++) {
                page2Size += g_audioData[g_currentPos + 27 + i];
            }
            g_currentPos += page2Size;
        }

        firstCall = false;
    }

    // 测试解码前10个包
    for (int i = 0; i < 10; i++) {
        SkOpusPacket packet;

        // 提取OPUS包
        sk_err_t ret = SkOggGetNextOpusPacket(downloadData->ringbuf, &packet);
        if (ret != SK_RET_SUCCESS) {
            if (ret == SK_RET_NOT_FOUND) {
                SK_LOGI(TAG, "No more packets to decode");
            } else {
                SK_LOGE(TAG, "Failed to get packet %d: %d", i+1, ret);
            }
            break;
        }

        packetCount++;

        // 解码OPUS包
        int32_t samples = SkOpusAdapterDecode(&packet, pcmBuffer, PCM_FRAME_SIZE);
        if (samples > 0) {
            successCount++;
            totalSamples += samples;

            SK_LOGI(TAG, "Packet %d: %zu bytes -> %ld samples",
                   packetCount, packet.size, samples);

            // 显示PCM数据的前几个样本
            if (samples >= 4) {
                SK_LOGI(TAG, "   PCM: %d %d %d %d...",
                       pcmBuffer[0], pcmBuffer[1], pcmBuffer[2], pcmBuffer[3]);
            }
        }

        SkOggFreeOpusPacket(&packet);
    }

    // 统计结果
    SK_LOGI(TAG, "=== Decoding Results ===");
    SK_LOGI(TAG, "Packets processed: %d", packetCount);
    SK_LOGI(TAG, "Successfully decoded: %d", successCount);
    SK_LOGI(TAG, "Total PCM samples: %zu", totalSamples);

    if (successCount > 0) {
        float duration = (float)totalSamples / 16000.0f;  // 16kHz采样率
        SK_LOGI(TAG, "Decoded audio duration: %.2f seconds", duration);
        SK_LOGI(TAG, "OPUS decoding test successful!");
    } else {
        SK_LOGE(TAG, "No packets decoded successfully");
    }

    free(pcmBuffer);
    return (successCount > 0) ? SK_RET_SUCCESS : SK_RET_FAIL;
}
